import { NextRequest, NextResponse } from 'next/server';
import { SchemaManager } from '@/lib/database/schema-manager';
import { SchemaEnricher } from '@/lib/ai/schema-enricher';
import { DatabaseConnection } from '@/lib/database/types';

export async function POST(request: NextRequest) {
  try {
    const connection: DatabaseConnection = await request.json();

    // التحقق من صحة البيانات
    if (!connection.host || !connection.database) {
      return NextResponse.json(
        { success: false, error: 'بيانات الاتصال غير مكتملة' },
        { status: 400 }
      );
    }

    // للـ SQL Server، التحقق من المصادقة
    if (connection.type === 'mssql' && !connection.useWindowsAuth && !connection.username) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم مطلوب لـ SQL Server Authentication' },
        { status: 400 }
      );
    }

    // للـ MySQL، اسم المستخدم مطلوب دائماً
    if (connection.type === 'mysql' && !connection.username) {
      return NextResponse.json(
        { success: false, error: 'اسم المستخدم مطلوب' },
        { status: 400 }
      );
    }

    const schemaManager = SchemaManager.getInstance();
    const schemaEnricher = new SchemaEnricher();

    // حفظ معلومات الاتصال (بدون كلمة المرور)
    await schemaManager.saveConnection(connection);

    // استخراج Schema الأساسي
    const schema = await schemaManager.extractSchema(connection);

    // إثراء Schema بالأوصاف
    const enrichedSchema = await schemaEnricher.enrichSchema(schema);

    // حفظ Schema المحسن
    await schemaManager.saveEnrichedSchema(enrichedSchema);

    return NextResponse.json({ 
      success: true, 
      schema: enrichedSchema,
      tablesCount: schema.tables.length
    });

  } catch (error) {
    console.error('خطأ في استخراج Schema:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'خطأ غير معروف' 
      },
      { status: 500 }
    );
  }
}
