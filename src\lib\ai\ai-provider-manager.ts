import { GoogleGeminiClient } from './google-gemini-client';
import { OpenRouterClient } from './openrouter-client';

export type AIProvider = 'gemini' | 'openrouter';

export interface AIProviderConfig {
  provider: AIProvider;
  model?: string;
}

/**
 * مدير موحد للتعامل مع مختلف مقدمي خدمات الذكاء الاصطناعي
 */
export class AIProviderManager {
  private static instance: AIProviderManager | null = null;
  private currentProvider: AIProvider = 'gemini';
  private geminiClient: GoogleGeminiClient | null = null;
  private openRouterClient: OpenRouterClient | null = null;

  private constructor() {}

  static getInstance(): AIProviderManager {
    if (!AIProviderManager.instance) {
      AIProviderManager.instance = new AIProviderManager();
    }
    return AIProviderManager.instance;
  }

  /**
   * تعيين مقدم الخدمة الحالي
   */
  setProvider(provider: AIProvider): void {
    this.currentProvider = provider;
    console.log(`تم تعيين مقدم الخدمة إلى: ${provider}`);
  }

  /**
   * الحصول على مقدم الخدمة الحالي
   */
  getCurrentProvider(): AIProvider {
    return this.currentProvider;
  }

  /**
   * الحصول على عميل الذكاء الاصطناعي الحالي
   */
  getCurrentClient(): GoogleGeminiClient | OpenRouterClient {
    switch (this.currentProvider) {
      case 'gemini':
        if (!this.geminiClient) {
          this.geminiClient = GoogleGeminiClient.getInstance();
        }
        return this.geminiClient;
      
      case 'openrouter':
        if (!this.openRouterClient) {
          this.openRouterClient = OpenRouterClient.getInstance();
        }
        return this.openRouterClient;
      
      default:
        throw new Error(`مقدم خدمة غير مدعوم: ${this.currentProvider}`);
    }
  }

  /**
   * توليد وصف للجدول باستخدام المقدم الحالي
   */
  async generateTableDescription(
    tableName: string,
    columns: Array<{name: string, type: string, nullable: boolean}>,
    foreignKeys: Array<{columnName: string, referencedTable: string}>,
    sampleData?: unknown[]
  ): Promise<{
    description: string;
    columnDescriptions: { [columnName: string]: string };
    analyticalValue: string;
    sqlExamples: Array<{
      query: string;
      explanation: string;
    }>;
    intelligentAnalysis: string;
    purpose: string;
    domain: string;
    businessContext: string;
    keyFields: string[];
    relatedTables: string[];
    limitations: string;
  }> {
    const client = this.getCurrentClient();
    return await client.generateTableDescription(tableName, columns, foreignKeys, sampleData);
  }

  /**
   * توليد استعلام SQL باستخدام المقدم الحالي
   */
  async generateSQLQuery(
    question: string,
    tablesInfo: Array<{
      name: string;
      description: string;
      columns: Array<{name: string, type: string}>;
      relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
    }>,
    databaseType: 'mysql' | 'mssql' = 'mysql'
  ): Promise<{
    query: string;
    explanation: string;
    confidence: number;
    relevantTables: string[];
  }> {
    const client = this.getCurrentClient();
    return await client.generateSQLQuery(question, tablesInfo, databaseType);
  }

  /**
   * توليد محتوى عام باستخدام المقدم الحالي
   */
  async generateContent(prompt: string): Promise<string> {
    const client = this.getCurrentClient();
    return await client.generateContent(prompt);
  }

  /**
   * تهيئة الوكيل الذكي باستخدام المقدم الحالي
   */
  async initializeIntelligentAgent(tablesInfo: Array<{
    name: string;
    description: string;
    columns: Array<{name: string, type: string}>;
    relationships: Array<{toTable: string, fromColumn: string, toColumn: string}>;
  }>): Promise<void> {
    const client = this.getCurrentClient();
    await client.initializeIntelligentAgent(tablesInfo);
  }

  /**
   * الحصول على قائمة بمقدمي الخدمة المتاحين
   */
  getAvailableProviders(): Array<{
    id: AIProvider;
    name: string;
    description: string;
    models: string[];
  }> {
    return [
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: 'نموذج Google Gemini 2.0 Flash - سريع ودقيق',
        models: ['gemini-2.0-flash-exp']
      },
      {
        id: 'openrouter',
        name: 'OpenRouter (Qwen)',
        description: 'نموذج Qwen 2.5 عبر OpenRouter - مجاني ومتقدم',
        models: ['qwen/qwen-2.5-72b-instruct']
      }
    ];
  }

  /**
   * فحص توفر مقدم الخدمة
   */
  async checkProviderAvailability(provider: AIProvider): Promise<{
    available: boolean;
    error?: string;
  }> {
    try {
      switch (provider) {
        case 'gemini':
          if (!process.env.GOOGLE_GEMINI_API_KEY) {
            return {
              available: false,
              error: 'GOOGLE_GEMINI_API_KEY غير موجود في متغيرات البيئة'
            };
          }
          break;
        
        case 'openrouter':
          if (!process.env.OPENROUTER_API_KEY) {
            return {
              available: false,
              error: 'OPENROUTER_API_KEY غير موجود في متغيرات البيئة'
            };
          }
          break;
        
        default:
          return {
            available: false,
            error: `مقدم خدمة غير مدعوم: ${provider}`
          };
      }

      return { available: true };
    } catch (error) {
      return {
        available: false,
        error: `خطأ في فحص توفر المقدم: ${error}`
      };
    }
  }

  /**
   * إعادة تعيين جميع العملاء
   */
  reset(): void {
    this.geminiClient = null;
    this.openRouterClient = null;
    this.currentProvider = 'gemini';
  }
}
