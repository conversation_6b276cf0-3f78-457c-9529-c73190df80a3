{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-21T20:09:45.245Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice - جدول يحتوي على 34 عمود، يتضمن معرف فريد، يحتوي على حقول أسماء، يتضمن معلومات زمنية", "columnDescriptions": {"ID": "عمود ID من نوع bigint - يحتاج إلى وصف يدوي", "DocumentName": "عمود DocumentName من نوع varchar - يحتاج إلى وصف يدوي", "RecordID": "عمود RecordID من نوع bigint - يحتاج إلى وصف يدوي", "TheNumber": "عمود TheNumber من نوع bigint - يحتاج إلى وصف يدوي", "SupplierName": "عمود SupplierName من نوع varchar - يحتاج إلى وصف يدوي", "InvoiceID": "عمود InvoiceID من نوع bigint - يحتاج إلى وصف يدوي", "DetailsID": "عمود DetailsID من نوع bigint - يحتاج إلى وصف يدوي", "TheDate": "عمود TheDate من نوع datetime - يحتاج إلى وصف يدوي", "CurrencyID": "عمود CurrencyID من نوع bigint - يحتاج إلى وصف يدوي", "TheMethod": "عمود TheMethod من نوع varchar - يحتاج إلى وصف يدوي"}, "analyticalValue": "لم يتم تحليل القيمة التحليلية بسبب خطأ في API", "sqlExamples": [{"query": "SELECT * FROM tbltemp_Inv_MainInvoice LIMIT 10", "explanation": "استعلام أساسي لعرض البيانات"}], "intelligentAnalysis": "تم توليد الوصف تلقائياً - يحتاج إلى مراجعة يدوية", "purpose": "إدارة المدفوعات والمعاملات المالية", "domain": "التجارة الإلكترونية", "businessContext": "تم توليد الوصف تلقائياً بناءً على بنية الجدول", "keyFields": ["ID"], "relatedTables": [], "limitations": "وصف تلقائي - قد لا يكون دقيقاً", "generatedAt": "2025-07-21T20:10:15.174Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain - جدول يحتوي على 69 عمود، يتضمن معرف فريد، يحتوي على حقول أسماء، يتضمن معلومات زمنية", "columnDescriptions": {"ID": "عمود ID من نوع bigint - يحتاج إلى وصف يدوي", "ParentID": "عمود ParentID من نوع bigint - يحتاج إلى وصف يدوي", "RowVersion": "عمود RowVersion من نوع timestamp - يحتاج إلى وصف يدوي", "DocumentID": "عمود DocumentID من نوع bigint - يحتاج إلى وصف يدوي", "RecordNumber": "عمود RecordNumber من نوع bigint - يحتاج إلى وصف يدوي", "RecordID": "عمود RecordID من نوع bigint - يحتاج إلى وصف يدوي", "TheDate": "عمود TheDate من نوع datetime - يحتاج إلى وصف يدوي", "ClientID": "عمود ClientID من نوع bigint - يحتاج إلى وصف يدوي", "DistributorID": "عمود DistributorID من نوع bigint - يحتاج إلى وصف يدوي", "CurrencyID": "عمود CurrencyID من نوع bigint - يحتاج إلى وصف يدوي"}, "analyticalValue": "لم يتم تحليل القيمة التحليلية بسبب خطأ في API", "sqlExamples": [{"query": "SELECT * FROM tbltemp_ItemsMain LIMIT 10", "explanation": "استعلام أساسي لعرض البيانات"}], "intelligentAnalysis": "تم توليد الوصف تلقائياً - يحتاج إلى مراجعة يدوية", "purpose": "إدارة المنتجات والسلع", "domain": "التجارة الإلكترونية", "businessContext": "تم توليد الوصف تلقائياً بناءً على بنية الجدول", "keyFields": ["ID"], "relatedTables": [], "limitations": "وصف تلقائي - قد لا يكون دقيقاً", "generatedAt": "2025-07-21T20:10:39.923Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}