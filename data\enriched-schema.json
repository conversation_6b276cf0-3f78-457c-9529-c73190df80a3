{"databaseName": "SalesTempDB", "databaseType": "mssql", "tables": [{"name": "tbltemp_Inv_MainInvoice", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SupplierName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "InvoiceID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DetailsID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "EnterTime", "type": "datetime", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "TotalAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MainUnitQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "MainUnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 20}, {"name": "MainUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "MCAmount", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "MainUnitBonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 37, "scale": 12}, {"name": "ExchangePrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "TotalAmountByCurrencyInvetory", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "NewSubItemEntryID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_Inv_MainInvoice", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 908}, {"name": "tbltemp_ItemsMain", "columns": [{"name": "ID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": true, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RowVersion", "type": "timestamp", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "DocumentID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "RecordID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheDate", "type": "datetime", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ClientID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "DistributorID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CurrencyID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheMethodID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Discount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Notes", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 255, "precision": null, "scale": null}, {"name": "UserID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "BranchID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "TheYear", "type": "int", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 10, "scale": 0}, {"name": "DocumentName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ClientName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "DistributorName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "TheMethod", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UserName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "BranchName", "type": "<PERSON><PERSON><PERSON>", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "CategoryID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CategoryName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CategoryNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemNumber", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "ItemTypeID", "type": "bigint", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ItemType", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "ReorderPoint", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 4}, {"name": "ISActive", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ISExpiry", "type": "bit", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "ExpiryPoint", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "UnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountFatherNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Account<PERSON><PERSON>", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "AccountNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "CostCenterName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 200, "precision": null, "scale": null}, {"name": "CostCenterNumber", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "Barcode", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitRank", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangeFactor", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "PackageQuantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "BarcodeID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "SerialNumber", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "UnitPrice", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ItemD<PERSON>unt", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "McItemDiscountCurrencyMain", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 13}, {"name": "Mc<PERSON>temDiscount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 38, "scale": 6}, {"name": "Quantity", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "Bonus", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExpiryDate", "type": "date", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": null, "scale": null}, {"name": "Amount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmount", "type": "numeric", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "MCAmountCurrencyMain", "type": "decimal", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "AccountID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "StoreName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "PackageUnitID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "PackageUnitName", "type": "<PERSON><PERSON><PERSON>", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": 150, "precision": null, "scale": null}, {"name": "NextParentID", "type": "bigint", "nullable": true, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 19, "scale": 0}, {"name": "ExchangePrice", "type": "numeric", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}, {"name": "ExchangePriceCurrencyInvetory", "type": "decimal", "nullable": false, "defaultValue": null, "isPrimaryKey": false, "isForeignKey": false, "maxLength": null, "precision": 18, "scale": 6}], "foreignKeys": [], "indexes": [{"name": "PK_tbltemp_ItemsMain", "columns": ["ID"], "isUnique": true, "isPrimary": true}], "primaryKeys": ["ID"], "rowCount": 0}], "relationships": [], "extractedAt": "2025-07-21T20:43:31.199Z", "version": "1.0", "tableDescriptions": [{"tableName": "tbltemp_Inv_MainInvoice", "description": "جدول tbltemp_Inv_MainInvoice يحتوي على بيانات الفواتير الرئيسية المؤقتة في نظام إدارة المخزون أو المحاسبة. يتم استخدام هذا الجدول لتخزين تفاصيل الفواتير مثل رقم الفاتورة، اسم المورد، التاريخ، العملة، طريقة الدفع، التفاصيل المالية، والبيانات الأخرى ذات الصلة.", "columnDescriptions": {"ID": "رقم التعريف الفريد لكل سجل في الجدول.", "DocumentName": "اسم الوثيقة المرتبطة بالفاتورة.", "RecordID": "رقم التعريف الخاص بالسجل الرئيسي المرتبط بالفاتورة.", "TheNumber": "رقم الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "SupplierName": "اسم المورد، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "InvoiceID": "رقم التعريف الفريد للفاتورة.", "DetailsID": "رقم التعريف الفريد لتفاصيل الفاتورة.", "TheDate": "تاريخ إصدار الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "CurrencyID": "رقم التعريف للعملة المستخدمة في الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "TheMethod": "طريقة الدفع المستخدمة في الفاتورة، يمكن أن تكون فارغة إذا لم يتم تحديدها.", "EnterTime": "وقت إدخال الفاتورة في النظام، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "ItemID": "رقم التعريف للصنف أو البند في الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "UnitID": "رقم التعريف للوحدة المستخدمة في الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "UnitPrice": "سعر الوحدة للصنف في الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "Quantity": "كمية الصنف في الفاتورة، يمكن أن تكون فارغة إذا لم يتم تحديدها.", "Bonus": "البونص أو الخصم المقدم مع الصنف، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "TotalAmount": "المبلغ الإجمالي للفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "MainUnitQuantity": "كمية الوحدة الرئيسية للصنف، يمكن أن تكون فارغة إذا لم يتم تحديدها.", "MainUnitPrice": "سعر الوحدة الرئيسية للصنف، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "MainUnitID": "رقم التعريف للوحدة الرئيسية للصنف، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "StoreID": "رقم التعريف للمخزن الذي تم تخزين الصنف فيه، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "BranchID": "رقم التعريف للفرع الذي تم إصدار الفاتورة منه، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "ExchangeFactor": "عامل التحويل بين الوحدات المختلفة للصنف.", "ClientID": "رقم التعريف للعميل، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "MCAmount": "المبلغ الإجمالي بالعملة المحلية، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "ExpiryDate": "تاريخ انتهاء الصلاحية للصنف، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "MainUnitBonus": "البونص أو الخصم المقدم مع الوحدة الرئيسية للصنف، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "ExchangePrice": "سعر الصرف للعملة المستخدمة في الفاتورة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "DistributorID": "رقم التعريف للموزع، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "DistributorName": "اسم الموزع، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "CostCenterID": "رقم التعريف لمركز التكلفة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "CostCenterName": "اسم مركز التكلفة، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "TotalAmountByCurrencyInvetory": "المبلغ الإجمالي للفاتورة بالعملة المخزنية، يمكن أن يكون فارغاً إذا لم يتم تحديده.", "NewSubItemEntryID": "رقم التعريف للبند الفرعي الجديد المدخل في الفاتورة."}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في تتبع وتحليل البيانات المالية المتعلقة بالفواتير، مثل حساب المبالغ الإجمالية، تحليل الربحية، ومتابعة الحركات المالية بين الموردين والعملاء. كما يمكن استخدامه لتحليل الأنماط في شراء الصنف وتحديد الفترات الزمنية الأكثر نشاطاً.", "sqlExamples": [{"query": "SELECT SupplierName, SUM(TotalAmount) AS TotalSpent FROM tbltemp_Inv_MainInvoice GROUP BY SupplierName ORDER BY TotalSpent DESC;", "explanation": "هذا الاستعلام يجمع المبالغ الإجمالية التي تم إنفاقها على كل مورد ويقوم بترتيب النتائج بشكل تنازلي حسب المبلغ الإجمالي. يمكن استخدامه لتحديد الموردين الأكثر إنفاقاً."}, {"query": "SELECT TheDate, COUNT(*) AS InvoiceCount FROM tbltemp_Inv_MainInvoice WHERE TheDate IS NOT NULL GROUP BY TheDate ORDER BY TheDate ASC;", "explanation": "هذا الاستعلام يحسب عدد الفواتير الصادرة في كل تاريخ ويقوم بترتيب النتائج بشكل تصاعدي حسب التاريخ. يمكن استخدامه لمتابعة نشاط الفواتير على مر الزمن."}, {"query": "SELECT ItemID, SUM(Quantity) AS TotalQuantity FROM tbltemp_Inv_MainInvoice WHERE ItemID IS NOT NULL GROUP BY ItemID ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يجمع الكمية الإجمالية لكل صنف ويقوم بترتيب النتائج بشكل تنازلي حسب الكمية. يمكن استخدامه لتحديد الصنوف الأكثر طلباً."}], "intelligentAnalysis": "الجدول يحتوي على بيانات متنوعة تتعلق بالفواتير، مما يسمح بإجراء تحليلات متعددة الأبعاد. يمكن استخدام البيانات لفهم الأنماط في الشراء، تحليل الربحية، ومتابعة الحركات المالية. كما يمكن ربط البيانات مع جداول أخرى مثل جدول العملاء والموردين لإجراء تحليلات أكثر تعقيداً.", "purpose": "الغرض الأساسي من الجدول هو تخزين تفاصيل الفواتير الرئيسية المؤقتة في نظام إدارة المخزون أو المحاسبة، مما يساعد في تتبع الحركات المالية والمخزنية.", "domain": "تجاري", "businessContext": "يستخدم الجدول في السياق التجاري لشركات البيع بالتجزئة والجملة، حيث يساعد في إدارة الفواتير والمخزون وتتبع الحركات المالية.", "keyFields": ["ID", "InvoiceID", "DetailsID", "RecordID"], "relatedTables": [], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من قدرة النظام على ربط البيانات بشكل فعال مع جداول أخرى. بالإضافة إلى ذلك، بعض الحقول يمكن أن تكون فارغة، مما قد يسبب صعوبات في التحليل إذا كانت هذه الحقول ضرورية للتحليل المطلوب.", "generatedAt": "2025-07-21T20:44:36.935Z"}, {"tableName": "tbltemp_ItemsMain", "description": "جدول tbltemp_ItemsMain يحتوي على بيانات تفصيلية للعناصر والبضائع في نظام إدارة المخزون والمستودعات. يتم استخدام هذا الجدول لتخزين معلومات متنوعة حول العناصر، مثل تفاصيل البضائع، العملاء، الموزعين، الوثائق، والعمليات المالية المرتبطة بها.", "columnDescriptions": {"ID": "المعرف الفريد للسجل في الجدول", "ParentID": "المعرف الفريد للسجل الأبوة (إذا كان موجودًا)", "RowVersion": "الختم الزمني للسجل، يستخدم لتعقب التغييرات", "DocumentID": "المعرف الفريد للوثيقة المرتبطة بالسجل", "RecordNumber": "رقم السجل في الوثيقة", "RecordID": "المعرف الفريد للسجل في الوثيقة", "TheDate": "التاريخ المرتبط بالسجل", "ClientID": "المعرف الفريد للعميل المرتبط بالسجل", "DistributorID": "المعرف الفريد للموزع المرتبط بالسجل", "CurrencyID": "المعرف الفريد للعملة المرتبطة بالسجل", "TheMethodID": "المعرف الفريد للطريقة المستخدمة في العملية", "Discount": "الخصم المطبق على العنصر", "Notes": "ملاحظات إضافية حول السجل", "UserID": "المعرف الفريد للمستخدم الذي قام بإدخال السجل", "BranchID": "المعرف الفريد للفرع المرتبط بالسجل", "TheYear": "السنة المرتبطة بالسجل", "DocumentName": "اسم الوثيقة المرتبطة بالسجل", "TheNumber": "الرقم المرتبط بالسجل", "ClientName": "اسم العميل المرتبط بالسجل", "DistributorName": "اسم الموزع المرتبط بالسجل", "CurrencyName": "اسم العملة المرتبطة بالسجل", "TheMethod": "اسم الطريقة المستخدمة في العملية", "UserName": "اسم المستخدم الذي قام بإدخال السجل", "BranchName": "اسم الفرع المرتبط بالسجل", "CategoryID": "المعرف الفريد للتصنيف المرتبط بالسجل", "FatherNumber": "رقم العنصر الأب في التصنيف", "CategoryName": "اسم التصنيف المرتبط بالسجل", "CategoryNumber": "رقم التصنيف المرتبط بالسجل", "ItemID": "المعرف الفريد للعنصر", "UnitID": "المعرف الفريد للوحدة المرتبطة بالعنصر", "ItemNumber": "رقم العنصر", "ItemName": "اسم العنصر", "ItemTypeID": "المعرف الفريد لنوع العنصر", "ItemType": "نوع العنصر", "ReorderPoint": "نقطة إعادة الطلب للعنصر", "ISActive": "حالة العنصر (نشط أو غير نشط)", "ISExpiry": "حالة انتهاء صلاحية العنصر", "ExpiryPoint": "نقطة انتهاء صلاحية العنصر", "UnitName": "اسم الوحدة المرتبطة بالعنصر", "AccountFatherNumber": "رقم الحساب الأب في التصنيف", "AccountName": "اسم الحساب المرتبط بالسجل", "AccountNumber": "رقم الحساب المرتبط بالسجل", "CostCenterID": "المعرف الفريد لمركز التكلفة المرتبط بالسجل", "CostCenterName": "اسم مركز التكلفة المرتبط بالسجل", "CostCenterNumber": "رقم مركز التكلفة المرتبط بالسجل", "Barcode": "الباركود المرتبط بالعنصر", "UnitRank": "ترتيب الوحدة المرتبطة بالعنصر", "ExchangeFactor": "عامل التبادل بين الوحدات", "PackageQuantity": "كمية الحزمة للعنصر", "BarcodeID": "المعرف الفريد للباركود المرتبط بالعنصر", "SerialNumber": "رقم التسلسل للعنصر", "UnitPrice": "سعر الوحدة للعنصر", "ItemDiscount": "خصم العنصر", "McItemDiscountCurrencyMain": "خصم العنصر بالعملة الرئيسية", "McItemDiscount": "خصم العنصر", "Quantity": "كمية العنصر", "Bonus": "البونص المقدم مع العنصر", "ExpiryDate": "تاريخ انتهاء صلاحية العنصر", "Amount": "المبلغ الإجمالي للعنصر", "MCAmount": "المبلغ الإجمالي للعنصر بالعملة الرئيسية", "MCAmountCurrencyMain": "المبلغ الإجمالي للعنصر بالعملة الرئيسية", "AccountID": "المعرف الفريد للحساب المرتبط بالسجل", "StoreID": "المعرف الفريد للمخزن المرتبط بالسجل", "StoreName": "اسم المخزن المرتبط بالسجل", "PackageUnitID": "المعرف الفريد للوحدة الحزمة المرتبطة بالعنصر", "PackageUnitName": "اسم الوحدة الحزمة المرتبطة بالعنصر", "NextParentID": "المعرف الفريد للسجل الأب التالي", "ExchangePrice": "سعر التبادل للعنصر", "ExchangePriceCurrencyInvetory": "سعر التبادل للعنصر بالعملة الرئيسية"}, "analyticalValue": "الجدول يوفر قيمة تحليلية كبيرة في مجال إدارة المخزون والمستودعات، حيث يمكن استخدامه لتحليل الأنماط الاستهلاكية، تقييم الأداء المالي، وتحسين عمليات الشراء والتخزين. يمكن استخدام البيانات لتحديد العناصر الأكثر مبيعًا، العناصر التي تحتاج إلى إعادة طلب، والعناصر التي تقترب من انتهاء صلاحيتها.", "sqlExamples": [{"query": "SELECT ItemName, SUM(Quantity) AS TotalQuantity FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY TotalQuantity DESC;", "explanation": "هذا الاستعلام يعرض العناصر الأكثر مبيعًا حسب الكمية الإجمالية المباعة، مما يساعد في تحديد العناصر الأكثر شعبية."}, {"query": "SELECT ItemName, AVG(UnitPrice) AS AveragePrice FROM tbltemp_ItemsMain GROUP BY ItemName ORDER BY AveragePrice DESC;", "explanation": "هذا الاستعلام يعرض متوسط سعر الوحدة لكل عنصر، مما يساعد في تحليل الأسعار وتحديد العناصر ذات الأسعار المرتفعة."}, {"query": "SELECT ItemName, COUNT(*) AS TotalRecords FROM tbltemp_ItemsMain WHERE ExpiryDate < GETDATE() GROUP BY ItemName ORDER BY TotalRecords DESC;", "explanation": "هذا الاستعلام يعرض العناصر التي انتهت صلاحيتها، مما يساعد في إدارة المخزون وتجنب الخسائر."}], "intelligentAnalysis": "الجدول يحتوي على بيانات متنوعة ومفصلة يمكن استخدامها لإجراء تحليلات ذكية. يمكن استخدام البيانات لبناء نماذج تنبؤية لتحديد الطلب المستقبلي، تحليل الأنماط الاستهلاكية، وتقييم الأداء المالي. كما يمكن استخدام البيانات لتحسين عمليات الشراء والتخزين وتجنب الخسائر الناتجة عن انتهاء صلاحية العناصر.", "purpose": "الغرض الأساسي من الجدول هو تخزين وتنظيم بيانات العناصر والبضائع في نظام إدارة المخزون والمستودعات، مما يساعد في تحسين كفاءة العمليات واتخاذ قرارات مستنيرة.", "domain": "تجاري", "businessContext": "السياق التجاري للجدول يشمل إدارة المخزون، الشراء، البيع، والعمليات المالية المرتبطة بالبضائع والمنتجات. يمكن استخدام البيانات لتحسين كفاءة العمليات، تقليل التكاليف، وزيادة الأرباح.", "keyFields": ["ID", "DocumentID", "ItemID", "TheDate", "Quantity", "UnitPrice", "ExpiryDate"], "relatedTables": ["tbltemp_Documents", "tbltemp_Clients", "tbltemp_Distributors", "tbltemp_Currencies", "tbltemp_Methods", "tbltemp_Users", "tbltemp_Branches", "tbltemp_Categories", "tbltemp_Accounts", "tbltemp_CostCenters", "tbltemp_Stores"], "limitations": "الجدول لا يحتوي على مفاتيح خارجية، مما قد يحد من قدرة النظام على ضمان سلامة البيانات وتوافقها. كما أن بعض الحقول يمكن أن تكون فارغة، مما قد يؤثر على دقة التحليلات والعمليات التي تعتمد على هذه الحقول.", "generatedAt": "2025-07-21T20:45:45.725Z"}], "embeddings": {"tbltemp_Inv_MainInvoice": [], "tbltemp_ItemsMain": []}}