{"name": "slt", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/genai": "^1.10.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-toast": "^1.2.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "langchain": "^0.3.30", "lucide-react": "^0.525.0", "mssql": "^11.0.1", "mysql2": "^3.14.2", "next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0", "recharts": "^3.1.0", "styled-jsx": "^5.1.7", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/mssql": "^9.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}